{"name": "frontend", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@mapbox/mapbox-gl-draw": "^1.4.3", "@mui/icons-material": "^5.16.7", "@mui/material": "^5.16.13", "@mui/x-charts": "^7.24.0", "@mui/x-data-grid": "^7.0.0", "@mui/x-date-pickers": "^7.24.0", "@turf/turf": "^7.1.0", "@types/react": "^17.0.83", "express": "^4.18.2", "framer-motion": "^11.3.19", "mapbox-gl": "^3.7.0", "maplibre-gl": "^4.7.1", "next": "14.1.0", "notistack": "^3.0.1", "prop-types": "^15.8.1", "react": "^18.3.1", "react-dom": "^18.3.1", "react-hook-form": "^7.49.3", "react-input-mask": "^2.0.4", "react-map-gl": "^7.1.7", "react-number-format": "^5.4.2", "recharts": "^2.15.0", "typed.js": "^2.1.0", "uuid": "^10.0.0"}, "devDependencies": {"@types/mapbox__mapbox-gl-draw": "^1.4.8", "@types/mapbox-gl": "^3.4.0", "@types/maplibre-gl": "^1.14.0", "@types/node": "^20", "@types/react-dom": "^18", "eslint": "^8", "eslint-config-next": "14.0.4", "typescript": "^5.3.3"}}